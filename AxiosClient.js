import axios from "axios";
import { getToken } from "./redux/slices/userSlice";
import { HOSTNAME } from "./config";

const axiosClient = axios.create({
  baseURL: HOSTNAME,
  headers: {
    "Content-Type": "application/json",
  },
});

axiosClient.interceptors.request.use(async (config) => {
  const token = await getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default axiosClient;
