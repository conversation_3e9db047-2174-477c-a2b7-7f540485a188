import { createSlice } from "@reduxjs/toolkit";

const cartSlice = createSlice({
  name: "cart",
  initialState: {
    selectedItem: null,
    selectedVendor: null,
  },
  reducers: {
    setSelectedItem: (state, action) => {
      state.selectedItem = action.payload;
    },
    setSelectedVendor: (state, action) => {
      state.selectedVendor = action.payload;
    },
  },
});

export const { setSelectedItem, setSelectedVendor } = cartSlice.actions;
export default cartSlice.reducer;
