import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ScrollView,
} from "react-native";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { AppImages } from "../utils/AppImages";
import { TextInput } from "react-native-gesture-handler";
import PersonalDetailsPopup from "../components/sections/checkout/PersonalDetailsPopup";

import EmptyCart from "../components/Empty/EmptyCart";
import { GetCheckoutApi, getSuggestions } from "../api/Checkout";
import SuggestedItemSlider from "../components/sections/checkout/SuggestedItemSlider";
import { useDispatch, useSelector } from "react-redux";
import { clearSelectedCoupon } from "../redux/slices/couponSlice";

const Card = ({ item, vendor }) => {
  const [quantity, setQuantity] = useState(item.quantity);

  const handleIncrease = () => {
    if (quantity < item.quantity) {
      setQuantity(quantity + 1);
    }
  };

  const handleDecrease = () => quantity > 1 && setQuantity(quantity - 1);

  return (
    <View style={styles.card}>
      <Image source={{ uri: item.image_url }} style={styles.image} />
      <View style={styles.infoContainer}>
        <View>
          <View style={styles.conatinerRoww}>
            <View style={{ flex: 1 }}>
              <Text style={styles.title}>
                {item.description.split(" ").length > 10
                  ? item.description.split(" ").slice(0, 10).join(" ") + "..."
                  : item.description}
              </Text>
              <Text style={styles.subtitle}>{item.item_type}</Text>
            </View>

            <TouchableOpacity style={styles.removeButton}>
              <Text style={styles.removeButtonText}>×</Text>
            </TouchableOpacity>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              paddingHorizontal: 0,
            }}
          >
            <Text style={[styles.price, { marginLeft: 0 }]}>
              {item.actual_price}
            </Text>
            <View style={[styles.quantityContainer, { marginRight: 0 }]}>
              <TouchableOpacity
                onPress={handleDecrease}
                style={styles.quantityButton}
              >
                <Text style={styles.quantityButtonText}>-</Text>
              </TouchableOpacity>
              <Text style={styles.quantity}>
                {quantity.toString().padStart(2, "0")}
              </Text>
              <TouchableOpacity
                onPress={handleIncrease}
                style={styles.IncreaseQuantityButton}
              >
                <Text style={styles.IncreaseQuantityButtonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const Cart = ({ navigation, route }) => {
  const vendor = useSelector((state) => state.cart.selectedVendor);
  const selectedCoupon = useSelector((state) => state.coupon.selectedCoupon);
  const dispatch = useDispatch();
  const [showEmptyCart, setShowEmptyCart] = useState(false);
  const [checkoutItems, setCheckoutItems] = useState([]);
  const [paymentBreakdown, setPaymentBreakdown] = useState(null);
  const [suggestions, setSuggestions] = useState([]);

  const fetchSuggetions = async (itemId, vendor_id) => {
    try {
      console.log("inside suggestion API..");
      console.log("ID_Item", itemId);
      console.log("Vendor_id", vendor_id);

      const response = await getSuggestions(vendor_id, itemId);

      setSuggestions(response);
    } catch (error) {
      console.error("Error fetching suggetions:", error);
    }
  };

  const GetCheckoutData = async () => {
    try {
      const response = await GetCheckoutApi();
      console.log("Get Checkout API response", response);

      const checkout = response[0];

      if (!checkout?.checkout_items?.length) {
        setShowEmptyCart(true);
        return;
      }

      const items = checkout.checkout_items.map((item) => ({
        id: item.id,
        image_url: item.image_url,
        description: item.description,
        item_type: item.item_type,
        actual_price: item.actual_price,
        quantity: item.quantity,
        vendor_id: checkout.vendor_id,
      }));

      setCheckoutItems(items);
      setPaymentBreakdown(checkout.payment_breakdown);
    } catch (error) {
      console.error("Error fetching checkout data:", error);
    }
  };

  useEffect(() => {
    GetCheckoutData();

    return () => {
      dispatch(clearSelectedCoupon());
    };
  }, []);

  useEffect(() => {
    if (checkoutItems.length > 0) {
      fetchSuggetions(checkoutItems[0].id, checkoutItems[0].vendor_id);
    }
  }, [checkoutItems]);

  const [firstModalVisible, setFirstModalVisible] = useState(false);

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingtext}>Cart</Text>
        </View>
      </View>
      {showEmptyCart ? (
        <EmptyCart />
      ) : (
        <ScrollView>
          <View style={styles.flatListConatiner}>
            <FlatList
              data={checkoutItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => <Card item={item} vendor={vendor} />}
            />
          </View>

          <View style={styles.secondSection}>
            <View style={styles.PromoCodeConatiner}>
              <View style={styles.container1}>
                <TextInput
                  value={selectedCoupon?.code || ""}
                  placeholder={selectedCoupon?.code ? "" : "Promo code"}
                  editable={true}
                  style={styles.textInput}
                  placeholderTextColor="#B0B0B0"
                />
                <View style={styles.promoInnerContainer}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate("Coupon")}
                  >
                    <Image source={AppImages.PROMO} style={styles.promoImage} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.copyButton}>
                    <Text style={styles.copyText}>Apply</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <View>
              <Text
                style={{
                  fontFamily: "Poppins_500Medium",
                  fontSize: 16,
                  marginTop: 14,
                }}
              >
                Complete your purchase with
              </Text>
              <SuggestedItemSlider data={suggestions} />
            </View>
            <View style={styles.billCard}>
              <Text style={styles.header}>Total Bill</Text>

              <View style={styles.row}>
                <Text style={styles.label}>
                  {" "}
                  Quantity (
                  {checkoutItems.reduce(
                    (acc, item) => acc + item.quantity,
                    0
                  )}{" "}
                  items)
                </Text>
                <Text style={styles.boldText}>
                  ${paymentBreakdown?.subtotal?.toFixed(2) || "0.00"}
                </Text>
              </View>

              {paymentBreakdown?.coupon_discount > 0 && (
                <View style={styles.row}>
                  <Text style={styles.label}>Coupon Discount</Text>
                  <Text style={styles.purpleText}>
                    -${paymentBreakdown.coupon_discount.toFixed(2)}
                  </Text>
                </View>
              )}

              {paymentBreakdown?.shipping_fee > 0 && (
                <View style={styles.row}>
                  <Text style={styles.label}>Shipping Fee</Text>
                  <Text style={styles.boldText}>
                    ${paymentBreakdown.shipping_fee.toFixed(2)}
                  </Text>
                </View>
              )}

              {paymentBreakdown?.platform_fee > 0 && (
                <View style={styles.row}>
                  <Text style={styles.label}>Platform Fee</Text>
                  <Text style={styles.boldText}>
                    ${paymentBreakdown.platform_fee.toFixed(2)}
                  </Text>
                </View>
              )}

              {paymentBreakdown?.platform_fee_gst > 0 && (
                <View style={styles.row}>
                  <Text style={styles.label}>Platform Fee GST</Text>
                  <Text style={styles.boldText}>
                    ${paymentBreakdown.platform_fee_gst.toFixed(2)}
                  </Text>
                </View>
              )}

              <View style={styles.totalRow}>
                <Text style={styles.boldText}>Total payment</Text>
                <View style={styles.total}>
                  {paymentBreakdown?.coupon_discount > 0 && (
                    <Text style={styles.strikeThrough}>
                      $
                      {(
                        paymentBreakdown.subtotal +
                        paymentBreakdown.platform_fee +
                        paymentBreakdown.platform_fee_gst
                      ).toFixed(2)}
                    </Text>
                  )}
                  <Text style={styles.finalAmount}>
                    ${paymentBreakdown?.final_amount?.toFixed(2) || "0.00"}
                  </Text>
                </View>
              </View>
            </View>
            <View style={styles.paymentContainer}>
              <Text style={styles.paymentMethod}>Payment method</Text>
              <View style={styles.cardDetails}>
                <Image source={AppImages.MASTERCARD_LOGO} />
                <Text style={styles.cardNumber}>**** **** **** 3095</Text>
                <TouchableOpacity
                  onPress={() => navigation.navigate("PaymentMethods")}
                  style={styles.changeButton}
                >
                  <Text style={styles.changeText}>Change</Text>
                </TouchableOpacity>
              </View>

              <PersonalDetailsPopup
                firstModalVisible={firstModalVisible}
                setFirstModalVisible={setFirstModalVisible}
              />

              <View style={styles.totalContainer}>
                <View>
                  <View style={styles.totalPriceInnerConatinner}>
                    <Text style={styles.totalText}>Total:</Text>
                    <Text style={styles.totalTextPrice}> $37,50</Text>
                  </View>

                  <Text style={styles.itemsText}>4 items</Text>
                </View>
                <TouchableOpacity
                  onPress={() => setFirstModalVisible(true)}
                  style={styles.payButton}
                >
                  <Text style={styles.payText}>Pay</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  conatinerRoww: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  flatListConatiner: {
    backgroundColor: "#FFF",
    padding: 20,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
  },
  secondSection: {
    paddingLeft: 20,
    paddingRight: 20,
    backgroundColor: "#FFFFFF",
  },
  PromoCodeConatiner: {
    backgroundColor: "#FBFBFBBA",
    padding: 16,
    paddingLeft: 20,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingRight: 20,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 6,
  },
  promoInnerConatiner: {
    flexDirection: "row",
    gap: 6,
    alignItems: "center",
  },
  totalPriceInnerConatinner: {
    flexDirection: "row",
    gap: 1,
    alignItems: "center",
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  headingtext: {
    fontFamily: "Poppins_600SemiBold",
    fontSize: 18,
    color: "#5F22D9",
    marginRight: 35,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    marginVertical: 5,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 40,
    resizeMode: "cover",
    overflow: "hidden",
  },
  infoContainer: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 5,
    paddingRight: 20,
    marginLeft: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#000000",
    flexShrink: 1,
    flexWrap: "wrap",
    maxWidth: 220,
  },

  subtitle: {
    fontSize: 14,
    color: "#8C8A9D",
  },
  price: {
    fontSize: 16,
    color: "#5F22D9",
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 10,
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
  },

  IncreaseQuantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    borderColor: "#5F22D9",
    backgroundColor: "#5F22D9",
    borderWidth: 2,
    alignItems: "center",
    justifyContent: "center",
    overflow: "hidden",
    shadowColor: "#AB4CFE",
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 6,
    elevation: 10,
  },
  quantityButtonText: {
    fontSize: 20,
    color: "#5F22D9",
    lineHeight: 20,
    textAlign: "center",
  },

  IncreaseQuantityButtonText: {
    fontSize: 20,
    color: "#FFF",
    lineHeight: 20,
    textAlign: "center",
  },

  quantity: {
    fontFamily: "Poppins_500Medium",
    marginHorizontal: 10,
    fontSize: 16,
    color: "#000",
  },
  removeButton: {
    width: 30,
    height: 30,
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  removeButtonText: {
    fontSize: 20,
    color: "#5F22D9",
  },
  container1: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFFFFF",
    borderRadius: 25,
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    width: "100%",
    alignSelf: "center",
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: "#000",
    marginRight: 10,
  },
  promoInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    maxWidth: "40%",
    flexShrink: 1,
  },
  promoImage: {
    width: 25,
    height: 25,
    resizeMode: "contain",
  },
  copyButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: 20,
  },
  copyText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontFamily: "Poppins_400Regular",
  },
  billCard: {
    backgroundColor: "#FBFBFBBA",
    borderRadius: 14,
    padding: 16,
    marginTop: 10,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.2,
    shadowRadius: 15,
    elevation: 10,
  },
  header: {
    fontSize: 18,
    fontFamily: "Poppins_500Medium",
    color: "#171725",
    marginBottom: 10,
  },
  rowContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 16,
  },
  label: {
    fontSize: 16,
  },
  boldText: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
  },
  purpleText: {
    fontSize: 16,
    color: "#5F22D9",
  },
  total: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  strikeThrough: {
    fontSize: 14,
    color: "#A0A0A0",
    textDecorationLine: "line-through",
    marginRight: 8,
  },
  finalAmount: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#5F22D9",
  },
  paymentContainer: {
    marginTop: 20,
  },
  paymentMethod: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 10,
    color: "#333",
  },
  cardDetails: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  cardNumber: {
    fontSize: 16,
    color: "#666",
    flex: 1,
    marginLeft: 10,
  },
  changeButton: {
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 17,
    borderColor: "#EDEFFF",
    borderWidth: 2,
  },
  changeText: {
    color: "#5F22D9",
    fontWeight: "bold",
    fontSize: 16,
  },
  totalContainer: {
    marginBottom: 20,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  totalText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  totalTextPrice: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  itemsText: {
    fontSize: 16,
    fontFamily: "Poppins_600SemiBold",
    color: "#898EBC",
  },
  payButton: {
    backgroundColor: "#5F22D9",
    paddingVertical: 18,
    paddingHorizontal: 70,
    borderRadius: 18,
    alignItems: "center",
  },
  payText: {
    color: "#FFF",
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default Cart;
