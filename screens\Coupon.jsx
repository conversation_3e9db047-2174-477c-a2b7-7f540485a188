import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  FlatList,
  Dimensions,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { AppImages } from "../utils/AppImages";
import GlobalStyles from "../styles/GlobalStyles";
import BackButton from "../components/common/buttons/BackButton";
import { CouponsData } from "../utils/Constants";
import { getCoupons } from "../api/Checkout";
import { useDispatch } from "react-redux";
import { setSelectedCoupon } from "../redux/slices/couponSlice";

const Coupon = ({ navigation }) => {
  const [activeCard, setActiveCard] = useState(1);
  const [coupons, setCoupons] = useState([]);
  const dispatch = useDispatch();

  const fetchCoupons = async () => {
    try {
      const response = await getCoupons();

      console.log("all coupons response");
      console.log(response);

      setCoupons(response);
    } catch (error) {
      console.error("Error fetching coupons:", error);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, []);

  const handleSelectCoupon = (item) => {
    console.log("selected item inside function");
    console.log(item);
    setActiveCard(item.id);
    dispatch(setSelectedCoupon(item));
    navigation.navigate("Cart");
  };

  const renderItem = ({ item }) => {
    const isActive = activeCard === item.id;

    return (
      <TouchableOpacity
        style={[styles.card, isActive && styles.activeCard]}
        onPress={() => handleSelectCoupon(item)}
      >
        <Image source={{ uri: item.image_url }} style={styles.logo} />
        <View style={styles.cardContent}>
          <Text style={[styles.title]}>{item.coupon_type}</Text>
          {item.code && <Text style={[styles.subtitle]}>{item?.code}</Text>}
        </View>
        <View
          style={[styles.radioButton, isActive && styles.activeRadioButton]}
        >
          {isActive && <View style={styles.innerCircle} />}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={GlobalStyles.androidSafeArea}>
      <View style={styles.container}>
        <BackButton navigation={navigation} />
        <View style={styles.titleContainer}>
          <Text style={styles.headingText}>Coupons</Text>
        </View>
      </View>
      <View style={styles.container1}>
        <Text style={styles.sectionTitle1}>Recommended</Text>
        <FlatList
          data={coupons}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={styles.listContainer}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    padding: 15,
  },
  headingText: {
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
    color: "#5F22D9",
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    marginTop: 50,
    position: "relative",
  },
  backButton: {
    padding: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowRadius: 10,
    shadowOffset: { width: 5, height: 5 },
    elevation: 5,
    position: "absolute",
    left: 16,
  },
  titleContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    fontFamily: "Poppins_500Medium",
    fontSize: 18,
  },
  secondConatiner: {
    marginTop: 10,
    backgroundColor: "#FFFFFF",
    height: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    gap: 16,
  },
  container1: {
    flex: 1,
    backgroundColor: "#F8F9FA",
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#8E8E8E",
    marginVertical: 12,
  },

  sectionTitle1: {
    fontSize: 16,
    fontFamily: "Poppins_500Medium",
    color: "#BABABA",
    marginVertical: 12,
    textAlign: "center",
    width: "100%",
  },

  container2: {
    padding: 16,
  },
  card: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    padding: 32,
    borderRadius: 25,
    marginBottom: 12,
    shadowColor: "#AB4CFE",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  activeCard: {
    borderWidth: 1,
    borderColor: "#5F22D9",
  },
  logo: {
    width: 35,
    height: 35,
    resizeMode: "contain",
    marginRight: 12,
  },
  cardContent: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: "Poppins_400Regular",
    color: "#181E22",
  },
  activeTitle: {
    color: "#181E22",
  },
  subtitle: {
    fontSize: 14,
    fontFamily: "Poppins_300Light",
    color: "#94979F",
  },
  activeSubtitle: {
    color: "#94979F",
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#6200ea",
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
  activeRadioButton: {
    borderColor: "#6200ea",
    backgroundColor: "#fff",
  },
  innerCircle: {
    width: 13,
    height: 13,
    borderRadius: 8,
    backgroundColor: "#6200ea",
  },
});

export default Coupon;
