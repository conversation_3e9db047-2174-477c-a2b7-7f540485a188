import axiosClient from "../AxiosClient";

export const CheckoutApi = async (data) => {
  const response = await axiosClient.post(`user/checkout/create`, data);
  return response.data;
};

export const GetCheckoutApi = async () => {
  const response = await axiosClient.get(`user/checkout/get`);
  return response.data;
};

export const getSuggestions = async (vendor_id, item_id) => {
  console.log("ids in parsms");
  console.log(vendor_id, item_id);
  const response = await axiosClient.get(
    `user/discover/items/suggestions/get?vendor_id=${vendor_id}&item_id=${item_id}`
  );
  console.log("Inside get suggestions API...");
  console.log(response.data);
  return response.data;
};

export const getCoupons = async () => {
  const response = await axiosClient.get(`user/checkout/coupon-code/get`);
  return response.data;
};
